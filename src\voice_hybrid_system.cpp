#include "voice_hybrid_system.h"
#include <esp_log.h>
#include <Audio.h>
#include <mutex>
#include <queue>
#include <FS.h>
#include <LittleFS.h>

// LittleFS is now provided by the built-in ESP32 Arduino framework

static const char *TAG = "VoiceHybrid";

// 外部声明
extern Audio audio;
extern String audio_path;
extern uint8_t audio_enable_flag;

// 全局变量
static bool system_initialized = false;
voice_system_config_t system_config; // 移除 static，使其可被其他文件访问
static voice_cache_entry_t *cache_head = nullptr;
static uint32_t cache_current_size = 0;
static std::mutex cache_mutex;
static std::mutex play_mutex;

// 内部函数声明
static voice_error_t find_best_voice_source(const char *voice_name, voice_file_info_t *info);
static voice_error_t load_from_rom(const char *voice_name, uint8_t **data, uint32_t *size);
static void cleanup_cache_lru(uint32_t required_size);

/**
 * @brief 初始化语音混合系统
 */
voice_error_t voice_hybrid_init(void)
{
    if (system_initialized)
    {
        ESP_LOGW(TAG, "Voice hybrid system already initialized");
        return VOICE_ERR_OK;
    }

    ESP_LOGI(TAG, "Initializing voice hybrid system...");

    // 设置默认配置
    system_config.priority_policy = VOICE_PRIORITY_SPEED_FIRST;
    system_config.cache_size_kb = VOICE_CACHE_SIZE_KB;
    system_config.compression_enabled = true;
    system_config.auto_update_enabled = true;
    system_config.update_check_interval = 3600; // 1小时

    // 初始化ROM文件系统
    voice_error_t err = voice_rom_init();
    if (err != VOICE_ERR_OK)
    {
        ESP_LOGE(TAG, "Failed to initialize ROM system: %d", err);
        return err;
    }

    // 初始化缓存
    cache_head = nullptr;
    cache_current_size = 0;

    system_initialized = true;
    ESP_LOGI(TAG, "Voice hybrid system initialized successfully");
    return VOICE_ERR_OK;
}

/**
 * @brief 反初始化语音混合系统
 */
voice_error_t voice_hybrid_deinit(void)
{
    if (!system_initialized)
    {
        return VOICE_ERR_OK;
    }

    ESP_LOGI(TAG, "Deinitializing voice hybrid system...");

    // 清理缓存
    voice_clear_cache();

    // 反初始化ROM系统
    voice_rom_deinit();

    system_initialized = false;
    ESP_LOGI(TAG, "Voice hybrid system deinitialized");
    return VOICE_ERR_OK;
}

/**
 * @brief 播放语音文件
 */
voice_error_t voice_play(const char *voice_name)
{
    if (!system_initialized || !voice_name)
    {
        return VOICE_ERR_INVALID_PARAM;
    }

    // 检查语音是否启用
    if (audio_enable_flag == 0)
    {
        ESP_LOGD(TAG, "Audio disabled, skipping playback");
        return VOICE_ERR_OK;
    }

    // 检查是否有语音正在播放
    if (audio.isRunning())
    {
        ESP_LOGD(TAG, "Audio is already playing, skipping");
        return VOICE_ERR_OK;
    }

    std::lock_guard<std::mutex> lock(play_mutex);

    ESP_LOGI(TAG, "Playing voice: %s", voice_name);

    // 查找最佳语音源
    voice_file_info_t file_info;
    voice_error_t err = find_best_voice_source(voice_name, &file_info);
    if (err != VOICE_ERR_OK)
    {
        ESP_LOGE(TAG, "Failed to find voice source for %s: %d", voice_name, err);
        return err;
    }

    ESP_LOGD(TAG, "Using voice source: %d, path: %s", file_info.source, file_info.file_path);

    // 根据源类型播放
    switch (file_info.source)
    {
    case VOICE_SOURCE_RAM_CACHE:
    case VOICE_SOURCE_LITTLEFS:
        // 直接从文件系统播放
        if (!audio.connecttoFS(LittleFS, file_info.file_path))
        {
            ESP_LOGE(TAG, "Failed to play from LittleFS: %s", file_info.file_path);
            return VOICE_ERR_INIT_FAILED;
        }
        break;

    case VOICE_SOURCE_FLASH_ROM:
        // 从ROM加载到临时文件播放
        {
            uint8_t *rom_data = nullptr;
            uint32_t rom_size = 0;

            err = load_from_rom(voice_name, &rom_data, &rom_size);
            if (err != VOICE_ERR_OK)
            {
                ESP_LOGE(TAG, "Failed to load from ROM: %d", err);
                return err;
            }

            // 创建临时文件
            String temp_path = audio_path + "/temp_" + String(voice_name) + ".wav";
            File temp_file = LittleFS.open(temp_path, "w");
            if (!temp_file)
            {
                ESP_LOGE(TAG, "Failed to create temp file: %s", temp_path.c_str());
                free(rom_data);
                return VOICE_ERR_INIT_FAILED;
            }

            temp_file.write(rom_data, rom_size);
            temp_file.close();
            free(rom_data);

            // 播放临时文件
            if (!audio.connecttoFS(LittleFS, temp_path.c_str()))
            {
                ESP_LOGE(TAG, "Failed to play temp file: %s", temp_path.c_str());
                LittleFS.remove(temp_path);
                return VOICE_ERR_INIT_FAILED;
            }

            // 异步删除临时文件（播放完成后）
            // 注意：这里简化处理，实际应该在播放完成回调中删除
        }
        break;

    case VOICE_SOURCE_DEFAULT_TONE:
        ESP_LOGW(TAG, "Playing default tone (not implemented)");
        return VOICE_ERR_OK;

    default:
        ESP_LOGE(TAG, "Invalid voice source: %d", file_info.source);
        return VOICE_ERR_INVALID_PARAM;
    }

    ESP_LOGI(TAG, "Voice playback started successfully");
    return VOICE_ERR_OK;
}

/**
 * @brief 查找最佳语音源
 */
static voice_error_t find_best_voice_source(const char *voice_name, voice_file_info_t *info)
{
    if (!voice_name || !info)
    {
        return VOICE_ERR_INVALID_PARAM;
    }

    memset(info, 0, sizeof(voice_file_info_t));
    info->source = VOICE_SOURCE_NONE;

    // 根据优先级策略选择源
    voice_source_type_t sources[4];
    int source_count = 0;

    switch (system_config.priority_policy)
    {
    case VOICE_PRIORITY_SPEED_FIRST:
        sources[0] = VOICE_SOURCE_RAM_CACHE;
        sources[1] = VOICE_SOURCE_LITTLEFS;
        sources[2] = VOICE_SOURCE_FLASH_ROM;
        sources[3] = VOICE_SOURCE_DEFAULT_TONE;
        source_count = 4;
        break;

    case VOICE_PRIORITY_QUALITY_FIRST:
        sources[0] = VOICE_SOURCE_LITTLEFS;
        sources[1] = VOICE_SOURCE_FLASH_ROM;
        sources[2] = VOICE_SOURCE_RAM_CACHE;
        sources[3] = VOICE_SOURCE_DEFAULT_TONE;
        source_count = 4;
        break;

    case VOICE_PRIORITY_RELIABILITY_FIRST:
        sources[0] = VOICE_SOURCE_FLASH_ROM;
        sources[1] = VOICE_SOURCE_LITTLEFS;
        sources[2] = VOICE_SOURCE_RAM_CACHE;
        sources[3] = VOICE_SOURCE_DEFAULT_TONE;
        source_count = 4;
        break;

    default:
        return VOICE_ERR_INVALID_PARAM;
    }

    // 按优先级顺序检查每个源
    for (int i = 0; i < source_count; i++)
    {
        switch (sources[i])
        {
        case VOICE_SOURCE_RAM_CACHE:
            // 检查RAM缓存（暂时跳过，因为缓存中的数据需要写入临时文件）
            break;

        case VOICE_SOURCE_LITTLEFS:
        {
            String file_path = audio_path + "/" + String(voice_name) + ".wav";
            if (LittleFS.exists(file_path))
            {
                info->source = VOICE_SOURCE_LITTLEFS;
                strncpy(info->file_path, file_path.c_str(), MAX_FILEPATH_LEN - 1);
                info->file_path[MAX_FILEPATH_LEN - 1] = '\0';

                File file = LittleFS.open(file_path, "r");
                if (file)
                {
                    info->file_size = file.size();
                    file.close();
                }
                return VOICE_ERR_OK;
            }
        }
        break;

        case VOICE_SOURCE_FLASH_ROM:
            if (voice_rom_file_exists(voice_name))
            {
                info->source = VOICE_SOURCE_FLASH_ROM;
                strncpy(info->file_path, voice_name, MAX_FILEPATH_LEN - 1);
                info->file_path[MAX_FILEPATH_LEN - 1] = '\0';

                voice_file_entry_t entry;
                if (voice_rom_get_file_entry(voice_name, &entry) == VOICE_ERR_OK)
                {
                    info->file_size = entry.original_size;
                    info->version = entry.version;
                    info->is_compressed = (entry.compression_type != VOICE_COMPRESSION_NONE);
                }
                return VOICE_ERR_OK;
            }
            break;

        case VOICE_SOURCE_DEFAULT_TONE:
            info->source = VOICE_SOURCE_DEFAULT_TONE;
            strncpy(info->file_path, "default_tone", MAX_FILEPATH_LEN - 1);
            return VOICE_ERR_OK;

        default:
            break;
        }
    }

    return VOICE_ERR_FILE_NOT_FOUND;
}

/**
 * @brief 从ROM加载语音文件
 */
static voice_error_t load_from_rom(const char *voice_name, uint8_t **data, uint32_t *size)
{
    if (!voice_name || !data || !size)
    {
        return VOICE_ERR_INVALID_PARAM;
    }

    // 获取文件信息
    voice_file_entry_t entry;
    voice_error_t err = voice_rom_get_file_entry(voice_name, &entry);
    if (err != VOICE_ERR_OK)
    {
        return err;
    }

    // 分配内存
    uint32_t buffer_size = entry.compressed_size;
    uint8_t *buffer = (uint8_t *)malloc(buffer_size);
    if (!buffer)
    {
        ESP_LOGE(TAG, "Failed to allocate memory for ROM file: %d bytes", buffer_size);
        return VOICE_ERR_INSUFFICIENT_MEMORY;
    }

    // 从ROM读取压缩数据
    uint32_t actual_size;
    err = voice_rom_read_file(voice_name, buffer, buffer_size, &actual_size);
    if (err != VOICE_ERR_OK)
    {
        free(buffer);
        return err;
    }

    // 如果需要解压缩
    if (entry.compression_type != VOICE_COMPRESSION_NONE)
    {
        uint8_t *decompressed_buffer = (uint8_t *)malloc(entry.original_size);
        if (!decompressed_buffer)
        {
            ESP_LOGE(TAG, "Failed to allocate memory for decompression: %d bytes", entry.original_size);
            free(buffer);
            return VOICE_ERR_INSUFFICIENT_MEMORY;
        }

        err = voice_decompress(buffer, actual_size, decompressed_buffer,
                               entry.original_size, entry.compression_type);
        if (err != VOICE_ERR_OK)
        {
            ESP_LOGE(TAG, "Failed to decompress ROM file: %d", err);
            free(buffer);
            free(decompressed_buffer);
            return err;
        }

        free(buffer);
        *data = decompressed_buffer;
        *size = entry.original_size;
    }
    else
    {
        *data = buffer;
        *size = actual_size;
    }

    ESP_LOGD(TAG, "Loaded ROM file %s: %d bytes", voice_name, *size);
    return VOICE_ERR_OK;
}

/**
 * @brief 清理缓存
 */
voice_error_t voice_clear_cache(void)
{
    std::lock_guard<std::mutex> lock(cache_mutex);

    voice_cache_entry_t *current = cache_head;
    while (current)
    {
        voice_cache_entry_t *next = current->next;
        if (current->data)
        {
            free(current->data);
        }
        free(current);
        current = next;
    }

    cache_head = nullptr;
    cache_current_size = 0;

    ESP_LOGI(TAG, "Cache cleared");
    return VOICE_ERR_OK;
}

/**
 * @brief 获取存储状态信息
 */
voice_error_t voice_get_storage_info(voice_storage_info_t *info)
{
    if (!info || !system_initialized)
    {
        return VOICE_ERR_INVALID_PARAM;
    }

    memset(info, 0, sizeof(voice_storage_info_t));

    // 获取ROM信息
    voice_rom_get_info(&info->rom_total_size, &info->rom_used_size, &info->rom_file_count);

    // 获取LittleFS信息
    info->littlefs_total_size = LittleFS.totalBytes();
    info->littlefs_used_size = LittleFS.usedBytes();

    // 计算LittleFS中的语音文件数量
    File root = LittleFS.open(audio_path);
    if (root && root.isDirectory())
    {
        File file = root.openNextFile();
        while (file)
        {
            if (!file.isDirectory() && String(file.name()).endsWith(".wav"))
            {
                info->littlefs_file_count++;
            }
            file = root.openNextFile();
        }
    }

    // 获取缓存信息
    std::lock_guard<std::mutex> lock(cache_mutex);
    info->cache_total_size = system_config.cache_size_kb * 1024;
    info->cache_used_size = cache_current_size;

    return VOICE_ERR_OK;
}

/**
 * @brief 设置优先级策略
 */
voice_error_t voice_set_priority_policy(voice_priority_policy_t policy)
{
    if (!system_initialized)
    {
        return VOICE_ERR_INIT_FAILED;
    }

    system_config.priority_policy = policy;
    ESP_LOGI(TAG, "Priority policy set to: %d", policy);
    return VOICE_ERR_OK;
}

/**
 * @brief 设置缓存大小
 */
voice_error_t voice_set_cache_size(uint32_t size_kb)
{
    if (!system_initialized)
    {
        return VOICE_ERR_INIT_FAILED;
    }

    // 如果新大小小于当前使用量，清理缓存
    uint32_t new_size_bytes = size_kb * 1024;
    if (new_size_bytes < cache_current_size)
    {
        cleanup_cache_lru(cache_current_size - new_size_bytes);
    }

    system_config.cache_size_kb = size_kb;
    ESP_LOGI(TAG, "Cache size set to: %d KB", size_kb);
    return VOICE_ERR_OK;
}

/**
 * @brief 启用/禁用压缩
 */
voice_error_t voice_set_compression_enabled(bool enabled)
{
    if (!system_initialized)
    {
        return VOICE_ERR_INIT_FAILED;
    }

    system_config.compression_enabled = enabled;
    ESP_LOGI(TAG, "Compression %s", enabled ? "enabled" : "disabled");
    return VOICE_ERR_OK;
}

/**
 * @brief 预加载语音文件到缓存
 */
voice_error_t voice_preload(const char *voice_name)
{
    if (!system_initialized || !voice_name)
    {
        return VOICE_ERR_INVALID_PARAM;
    }

    ESP_LOGI(TAG, "Preloading voice file: %s", voice_name);

    // 查找最佳语音源
    voice_file_info_t file_info;
    voice_error_t err = find_best_voice_source(voice_name, &file_info);
    if (err != VOICE_ERR_OK)
    {
        ESP_LOGW(TAG, "Failed to find voice source for preload: %s", voice_name);
        return err;
    }

    // 如果文件已经在缓存中，更新访问时间
    std::lock_guard<std::mutex> lock(cache_mutex);
    voice_cache_entry_t *current = cache_head;
    while (current)
    {
        if (strcmp(current->voice_name, voice_name) == 0)
        {
            current->last_access_time = millis();
            ESP_LOGD(TAG, "Voice file already in cache: %s", voice_name);
            return VOICE_ERR_OK;
        }
        current = current->next;
    }

    // 如果是 LittleFS 文件，加载到缓存
    if (file_info.source == VOICE_SOURCE_LITTLEFS)
    {
        File file = LittleFS.open(file_info.file_path, "r");
        if (!file)
        {
            ESP_LOGE(TAG, "Failed to open file for preload: %s", file_info.file_path);
            return VOICE_ERR_FILE_NOT_FOUND;
        }

        uint32_t file_size = file.size();
        uint8_t *data = (uint8_t *)malloc(file_size);
        if (!data)
        {
            ESP_LOGE(TAG, "Failed to allocate memory for preload: %d bytes", file_size);
            file.close();
            return VOICE_ERR_INSUFFICIENT_MEMORY;
        }

        file.read(data, file_size);
        file.close();

        // 检查缓存空间
        uint32_t max_cache_size = system_config.cache_size_kb * 1024;
        if (cache_current_size + file_size > max_cache_size)
        {
            cleanup_cache_lru(cache_current_size + file_size - max_cache_size);
        }

        // 创建缓存条目
        voice_cache_entry_t *entry = (voice_cache_entry_t *)malloc(sizeof(voice_cache_entry_t));
        if (!entry)
        {
            ESP_LOGE(TAG, "Failed to allocate cache entry");
            free(data);
            return VOICE_ERR_INSUFFICIENT_MEMORY;
        }

        strncpy(entry->voice_name, voice_name, MAX_FILENAME_LEN - 1);
        entry->voice_name[MAX_FILENAME_LEN - 1] = '\0';
        entry->data = data;
        entry->size = file_size;
        entry->last_access_time = millis();
        entry->next = cache_head;
        cache_head = entry;
        cache_current_size += file_size;

        ESP_LOGI(TAG, "Successfully preloaded voice file: %s (%d bytes)", voice_name, file_size);
    }
    else
    {
        ESP_LOGD(TAG, "Voice file not suitable for caching: %s (source: %d)", voice_name, file_info.source);
    }

    return VOICE_ERR_OK;
}

/**
 * @brief LRU缓存清理
 */
static void cleanup_cache_lru(uint32_t required_size)
{
    // 简化的LRU实现：删除最旧的条目直到释放足够空间
    uint32_t freed_size = 0;
    voice_cache_entry_t *prev = nullptr;
    voice_cache_entry_t *current = cache_head;
    voice_cache_entry_t *oldest = nullptr;
    voice_cache_entry_t *oldest_prev = nullptr;

    while (freed_size < required_size && cache_head)
    {
        // 找到最旧的条目
        uint32_t oldest_time = UINT32_MAX;
        prev = nullptr;
        current = cache_head;
        oldest = nullptr;
        oldest_prev = nullptr;

        while (current)
        {
            if (current->last_access_time < oldest_time)
            {
                oldest_time = current->last_access_time;
                oldest = current;
                oldest_prev = prev;
            }
            prev = current;
            current = current->next;
        }

        if (oldest)
        {
            // 删除最旧的条目
            if (oldest_prev)
            {
                oldest_prev->next = oldest->next;
            }
            else
            {
                cache_head = oldest->next;
            }

            freed_size += oldest->size;
            cache_current_size -= oldest->size;

            if (oldest->data)
            {
                free(oldest->data);
            }
            free(oldest);

            ESP_LOGD(TAG, "Removed cache entry, freed %d bytes", oldest->size);
        }
        else
        {
            break;
        }
    }

    ESP_LOGD(TAG, "Cache cleanup completed, freed %d bytes", freed_size);
}
